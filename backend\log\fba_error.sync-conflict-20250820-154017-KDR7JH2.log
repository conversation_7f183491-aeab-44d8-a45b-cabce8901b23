2025-08-20 07:52:26.269 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000001A4F91AA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000001A4FB678B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001A4FB67BA60>
    └ <uvicorn.server.Server object at 0x000001A482591DC0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001A4FB67BB00>
           │       │   └ <uvicorn.server.Server object at 0x000001A482591DC0>
           │       └ <function run at 0x000001A4FAE7F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001A483691B60>
           │      └ <function Runner.run at 0x000001A4FAF1B2E0>
           └ <asyncio.runners.Runner object at 0x000001A4824B3560>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001A4FAF18EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001A4824B3560>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001A4FAFE8D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001A4FAF1AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001A4FAE74860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=908, family=2, type=1, proto=6, laddr=('*************', 57759), raddr=('*************', 5981)>
    └ <_ProactorSocketTransport closing fd=908>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-20 07:52:26.624 | ERROR    | 3fa9e7472e60409eabda1b2ae26c0090 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 07:54:05.223 | ERROR    | 0562cb4d14a14753a037097687d568b8 | RAGFlow文档服务请求超时: http://*************:6610/api/v1/datasets/b443fee27ccb11f09631ea5dc8d5776c/documents
2025-08-20 07:54:05.224 | ERROR    | 0562cb4d14a14753a037097687d568b8 | 上传文档失败: RAGFlow文档服务请求超时
2025-08-20 08:33:52.741 | ERROR    | 109fbe6733064a808e14ef42ffeb1d67 | RAGFlow文档服务请求超时: http://*************:6610/api/v1/datasets/b443fee27ccb11f09631ea5dc8d5776c/documents
2025-08-20 08:33:52.741 | ERROR    | 109fbe6733064a808e14ef42ffeb1d67 | 上传文档失败: RAGFlow文档服务请求超时
2025-08-20 08:36:47.610 | ERROR    | b81ff0e63a3549fc9055c4c260758801 | RAGFlow文档服务请求超时: http://*************:6610/api/v1/datasets/b443fee27ccb11f09631ea5dc8d5776c/documents
2025-08-20 08:36:47.610 | ERROR    | b81ff0e63a3549fc9055c4c260758801 | 获取文档列表失败: RAGFlow文档服务请求超时
2025-08-20 08:37:04.454 | ERROR    | d6f7cffa9a434969936f956b1042912c | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
              │   └ <function verify at 0x0000029DDD0A3C40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xe8\x1fP\x8dE>\xeaF\xcb\x01\x93\xcd\n\x07\xd9\x8a'\xb9s\x1c8\x8d\x99\xf5\xaf\xb36tRd\x0e.\t\x19\xdb!(\xb5\x96JX\xb9\x8a\xd...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9'
    └ <function _verify_signature at 0x0000029DDD18B740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x0000029DDD0A3A60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
                    └ <function jwt_decode at 0x0000029DDD201F80>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000029DD634A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000029DD88B8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000029DD88BBA60>
    └ <uvicorn.server.Server object at 0x0000029DDE5BFFB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000029DD88BBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000029DDE5BFFB0>
           │       └ <function run at 0x0000029DD80FF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000029DE07E1A80>
           │      └ <function Runner.run at 0x0000029DD819B2E0>
           └ <asyncio.runners.Runner object at 0x0000029DE07D0050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029DD8198EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000029DE07D0050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000029DD8268D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029DD819AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000029DD80F4860>
    └ <Handle Task.task_wakeup(<Future cancelled>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future cancelled>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000029DE0A89800>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000029DE0A89EE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000029DDF68F4A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000029DE0A89800>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000029DE0A89EE0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000029DDB8F1260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000029DE0A89EE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000029DE088EBD0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x0000029DE0AC9B80>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000029DDC8B77E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000029DDE5BE540>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000029DE088EBD0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
                 └ <function jwt_authentication at 0x0000029DDD202480>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000029DDD201C60>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000029DDE1EDA00>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-20 08:37:34.471 | ERROR    | 8149167c957d47ed8f57dec4ccc506d0 | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
              │   └ <function verify at 0x0000029DDD0A3C40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xe8\x1fP\x8dE>\xeaF\xcb\x01\x93\xcd\n\x07\xd9\x8a'\xb9s\x1c8\x8d\x99\xf5\xaf\xb36tRd\x0e.\t\x19\xdb!(\xb5\x96JX\xb9\x8a\xd...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9'
    └ <function _verify_signature at 0x0000029DDD18B740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x0000029DDD0A3A60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
                    └ <function jwt_decode at 0x0000029DDD201F80>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000029DD634A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000029DD88B8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000029DD88BBA60>
    └ <uvicorn.server.Server object at 0x0000029DDE5BFFB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000029DD88BBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000029DDE5BFFB0>
           │       └ <function run at 0x0000029DD80FF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000029DE07E1A80>
           │      └ <function Runner.run at 0x0000029DD819B2E0>
           └ <asyncio.runners.Runner object at 0x0000029DE07D0050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029DD8198EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000029DE07D0050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000029DD8268D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029DD819AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000029DD80F4860>
    └ <Handle Task.task_wakeup(<Future cancelled>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future cancelled>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000029DE0A894E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000029DE0A8B240>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000029DDF68F4A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000029DE0A894E0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000029DE0A8B240>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000029DDB8F1260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000029DE0A8B240>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000029DE088EBD0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x0000029DE0A9F410>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000029DDC8B77E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000029DDE5BE540>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000029DE088EBD0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
                 └ <function jwt_authentication at 0x0000029DDD202480>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000029DDD201C60>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000029DDE1EDA00>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-20 08:38:04.488 | ERROR    | e3bdd1c81e5d48a9b57be0162d20def3 | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
              │   └ <function verify at 0x0000029DDD0A3C40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xe8\x1fP\x8dE>\xeaF\xcb\x01\x93\xcd\n\x07\xd9\x8a'\xb9s\x1c8\x8d\x99\xf5\xaf\xb36tRd\x0e.\t\x19\xdb!(\xb5\x96JX\xb9\x8a\xd...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9'
    └ <function _verify_signature at 0x0000029DDD18B740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x0000029DDD0A3A60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
                    └ <function jwt_decode at 0x0000029DDD201F80>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000029DD634A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000029DD88B8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000029DD88BBA60>
    └ <uvicorn.server.Server object at 0x0000029DDE5BFFB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000029DD88BBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000029DDE5BFFB0>
           │       └ <function run at 0x0000029DD80FF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000029DE07E1A80>
           │      └ <function Runner.run at 0x0000029DD819B2E0>
           └ <asyncio.runners.Runner object at 0x0000029DE07D0050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029DD8198EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000029DE07D0050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000029DD8268D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029DD819AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000029DD80F4860>
    └ <Handle Task.task_wakeup(<Future cancelled>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future cancelled>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000029DE0A88F40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000029DE0A896C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000029DDF68F4A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000029DE0A88F40>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000029DE0A896C0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000029DDB8F1260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000029DE0A896C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000029DE088EBD0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029DE088EB70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x0000029DE0A858E0>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000029DDC8B77E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000029DDE5BE540>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000029DE088EBD0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
                 └ <function jwt_authentication at 0x0000029DDD202480>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE2MDBkMDRjLTcyZDctNDZmZS05ZTMxLTJkYzkzOTYwOTFiZiJ9.6B9QjUU-6kbLAZPNCgfZiie5cx...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000029DDD201C60>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000029DDE1EDA00>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-20 08:53:39.856 | ERROR    | e629f069b9d6454fa722a0d1a1b0e6b7 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:41.929 | ERROR    | 8bab3c5eeee14856847c7377cb51748a | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:42.784 | ERROR    | e1ab7cd32c7345169c745816d6afdd7a | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:43.775 | ERROR    | ca51d52972aa46eaa770f5dda48052ae | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:44.475 | ERROR    | ba0367f2c22345dfa36607e57c9a3659 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:44.898 | ERROR    | a7b17a11133a43e0a7fbd2ea1d1b61b6 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:45.270 | ERROR    | 5e27c908752b422ca9df744f4bdab4a9 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:45.554 | ERROR    | 293366beaa1a4a989a40eaac8032d955 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:45.819 | ERROR    | 98398d44a87c4c2ea6941d98bc14dba6 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:46.053 | ERROR    | c2ddce3bf3fa4bf88956f6d5b72c48d9 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:46.963 | ERROR    | 29261f050aa94d5c9e7528955841dbc4 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 08:53:47.651 | ERROR    | e87830b6311f42999b584e9602f52fca | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 09:02:22.163 | ERROR    | - | Exception in callback BaseProactorEventLoop._start_serving.<locals>.loop(<_OverlappedF...0.1', 54853))>) at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py:843
handle: <Handle BaseProactorEventLoop._start_serving.<locals>.loop(<_OverlappedF...0.1', 54853))>) at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py:843>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002A81100A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002A8134E8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002A8134EBA60>
    └ <uvicorn.server.Server object at 0x000002A819F37CE0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002A8134EBB00>
           │       │   └ <uvicorn.server.Server object at 0x000002A819F37CE0>
           │       └ <function run at 0x000002A812CDF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002A81B491B60>
           │      └ <function Runner.run at 0x000002A81317B2E0>
           └ <asyncio.runners.Runner object at 0x000002A81B3F8980>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002A813178EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002A81B3F8980>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002A813248D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002A81317AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002A812CD4860>
    └ <Handle BaseProactorEventLoop._start_serving.<locals>.loop(<_OverlappedF...0.1', 54853))>) at C:\Users\<USER>\AppData...
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle BaseProactorEventLoop._start_serving.<locals>.loop(<_OverlappedF...0.1', 54853))>) at C:\Users\<USER>\AppData...
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle BaseProactorEventLoop._start_serving.<locals>.loop(<_OverlappedF...0.1', 54853))>) at C:\Users\<USER>\AppData...
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle BaseProactorEventLoop._start_serving.<locals>.loop(<_OverlappedF...0.1', 54853))>) at C:\Users\<USER>\AppData...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 858, in loop
    self._make_socket_transport(
    │    └ <function BaseProactorEventLoop._make_socket_transport at 0x000002A8131A3D80>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 647, in _make_socket_transport
    return _ProactorSocketTransport(self, sock, protocol, waiter,
           │                        │     │     │         └ None
           │                        │     │     └ <uvicorn.protocols.http.httptools_impl.HttpToolsProtocol object at 0x000002A81B64DB80>
           │                        │     └ <socket.socket fd=1900, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 54853)>
           │                        └ <ProactorEventLoop running=True closed=False debug=False>
           └ <class 'asyncio.proactor_events._ProactorSocketTransport'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 613, in __init__
    super().__init__(loop, sock, protocol, waiter, extra, server)
                     │     │     │         │       │      └ <Server sockets=()>
                     │     │     │         │       └ {'peername': ('127.0.0.1', 54853), 'socket': <asyncio.TransportSocket fd=1900, family=2, type=1, proto=0, laddr=('127.0.0.1',...
                     │     │     │         └ None
                     │     │     └ <uvicorn.protocols.http.httptools_impl.HttpToolsProtocol object at 0x000002A81B64DB80>
                     │     └ <socket.socket fd=1900, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 54853)>
                     └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 189, in __init__
    super().__init__(loop, sock, protocol, waiter, extra, server)
                     │     │     │         │       │      └ <Server sockets=()>
                     │     │     │         │       └ {'peername': ('127.0.0.1', 54853), 'socket': <asyncio.TransportSocket fd=1900, family=2, type=1, proto=0, laddr=('127.0.0.1',...
                     │     │     │         └ None
                     │     │     └ <uvicorn.protocols.http.httptools_impl.HttpToolsProtocol object at 0x000002A81B64DB80>
                     │     └ <socket.socket fd=1900, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 54853)>
                     └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 335, in __init__
    super().__init__(*args, **kw)
                      │       └ {}
                      └ (<ProactorEventLoop running=True closed=False debug=False>, <socket.socket fd=1900, family=2, type=1, proto=0, laddr=('127.0....
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 66, in __init__
    self._server._attach()
    │    │       └ <function Server._attach at 0x000002A813177880>
    │    └ <Server sockets=()>
    └ <_ProactorSocketTransport fd=1900>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 296, in _attach
    assert self._sockets is not None
           │    └ None
           └ <Server sockets=()>

AssertionError: assert self._sockets is not None
2025-08-20 09:02:56.744 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002894351A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000028945A28B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000028945A2BA60>
    └ <uvicorn.server.Server object at 0x000002894C7C8C20>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000028945A2BB00>
           │       │   └ <uvicorn.server.Server object at 0x000002894C7C8C20>
           │       └ <function run at 0x000002894523F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002894D9D1B60>
           │      └ <function Runner.run at 0x00000289456DB2E0>
           └ <asyncio.runners.Runner object at 0x000002894C8885C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000289456D8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002894C8885C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000289457A8D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000289456DAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000028945234860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1780, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 56745)>
    └ <_ProactorSocketTransport closing fd=1780>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-20 09:03:15.509 | ERROR    | 9da7821fca1a40b79c2330b12bb03c07 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 09:03:16.724 | ERROR    | 975b2bb883054b559110f90964f54429 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 09:03:17.044 | ERROR    | 39cb3fc18fd245dba02ffb6f86b4b12e | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 09:03:17.460 | ERROR    | 83c74fae59c741b0ab48eab3a3864696 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 09:03:17.636 | ERROR    | 46134ed0015141ee91b278112a8afea4 | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 09:04:50.391 | ERROR    | 346111b77f9e44f3b60108494a326abc | 停止文档解析失败: Can't stop parsing document with progress at 0 or 1
2025-08-20 09:09:18.972 | ERROR    | 73d18b44de864439928b105d29dc3e7c | RAGFlow文档服务请求超时: http://*************:6610/api/v1/datasets/b443fee27ccb11f09631ea5dc8d5776c/chunks
2025-08-20 09:09:18.972 | ERROR    | 73d18b44de864439928b105d29dc3e7c | 开始文档解析失败: RAGFlow文档服务请求超时
2025-08-20 09:13:10.456 | ERROR    | 86797dc637674f31886fb633cf351f1c | RAGFlow文档服务请求超时: http://*************:6610/api/v1/datasets/b443fee27ccb11f09631ea5dc8d5776c/documents
2025-08-20 09:13:10.456 | ERROR    | 86797dc637674f31886fb633cf351f1c | 获取文档列表失败: RAGFlow文档服务请求超时
2025-08-20 10:32:30.759 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000001798A41A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000001798C948B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001798C94BA60>
    └ <uvicorn.server.Server object at 0x0000017994811460>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001798C94BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000017994811460>
           │       └ <function run at 0x000001798C13B060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000179948EDA80>
           │      └ <function Runner.run at 0x000001798C1DB2E0>
           └ <asyncio.runners.Runner object at 0x0000017993800C50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001798C1D8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000017993800C50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001798C2A8D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001798C1DAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001798C134860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1700, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 57329)>
    └ <_ProactorSocketTransport closing fd=1700>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-20 10:33:25.734 | ERROR    | d57b9c7fb349400d8a6b18e3d119c24a | RAGFlow文档服务请求超时: http://*************:6610/api/v1/datasets/b443fee27ccb11f09631ea5dc8d5776c/documents
2025-08-20 10:33:25.735 | ERROR    | d57b9c7fb349400d8a6b18e3d119c24a | 上传文档失败: RAGFlow文档服务请求超时
2025-08-20 11:25:02.463 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to *************:5862. 信号灯超时时间已到.
2025-08-20 14:11:41.742 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x00000244F37DA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x00000244F5CD8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000244F5CDBA60>
    └ <uvicorn.server.Server object at 0x00000244FC82BBF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000244F5CDBB00>
           │       │   └ <uvicorn.server.Server object at 0x00000244FC82BBF0>
           │       └ <function run at 0x00000244F550F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000244FDC81D20>
           │      └ <function Runner.run at 0x00000244F55AB2E0>
           └ <asyncio.runners.Runner object at 0x00000244FDBA5160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000244F55A8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000244FDBA5160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000244F5678D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000244F55AAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000244F5504860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1892, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 64243)>
    └ <_ProactorSocketTransport closing fd=1892>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
