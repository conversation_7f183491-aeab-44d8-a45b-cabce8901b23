#!/usr/bin/env python3
"""
测试 pythontest5 知识库中 test (5).pptx 文档的分块更新功能

使用方法:
python test-pythontest5-chunk-update.py --token your_jwt_token

这个脚本会：
1. 自动查找 pythontest5 知识库
2. 查找 test (5).pptx 文档
3. 获取第一个分块
4. 测试更新功能
5. 验证更新是否生效
"""

import argparse
import requests
import json
import time
from typing import Dict, Any, Optional

class PythonTest5ChunkTester:
    def __init__(self, token: str):
        self.base_url = "http://localhost:8000"
        self.token = token
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        self.kb_id = None
        self.doc_id = None
        self.chunk_id = None

    def log(self, message: str, level: str = "INFO"):
        """打印日志"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        colors = {
            "INFO": "\033[94m",      # 蓝色
            "SUCCESS": "\033[92m",   # 绿色
            "WARNING": "\033[93m",   # 黄色
            "ERROR": "\033[91m",     # 红色
            "RESET": "\033[0m"       # 重置
        }
        color = colors.get(level, colors["INFO"])
        reset = colors["RESET"]
        print(f"{color}[{timestamp}] {level}: {message}{reset}")

    def make_request(self, method: str, endpoint: str, data: Dict[Any, Any] = None) -> Optional[Dict[Any, Any]]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=self.headers, json=data)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            self.log(f"{method} {endpoint} - Status: {response.status_code}")
            
            if response.status_code >= 400:
                self.log(f"请求失败: {response.text}", "ERROR")
                return None

            return response.json()

        except Exception as e:
            self.log(f"请求异常: {str(e)}", "ERROR")
            return None

    def find_pythontest5_kb(self) -> bool:
        """查找 pythontest5 知识库"""
        self.log("🔍 查找 pythontest5 知识库...")
        
        result = self.make_request('GET', '/api/iot/v1/knowledge-bases?page=1&page_size=100')
        
        if not result or result.get("code") != 200:
            self.log("❌ 获取知识库列表失败", "ERROR")
            return False
        
        knowledge_bases = result.get("data", {}).get("knowledge_bases", [])
        
        for kb in knowledge_bases:
            kb_name = kb.get("name", "").lower()
            if "pythontest5" in kb_name or "python test 5" in kb_name or "python_test_5" in kb_name:
                self.kb_id = kb.get("id")
                self.log(f"✅ 找到知识库: {kb.get('name')} (ID: {self.kb_id})", "SUCCESS")
                return True
        
        self.log("❌ 未找到 pythontest5 知识库", "ERROR")
        self.log("📋 可用的知识库:")
        for kb in knowledge_bases[:10]:  # 只显示前10个
            self.log(f"   - {kb.get('name')} (ID: {kb.get('id')})")
        
        return False

    def find_test5_document(self) -> bool:
        """查找 test (5).pptx 文档"""
        self.log(f"🔍 在知识库 {self.kb_id} 中查找 test (5).pptx 文档...")
        
        result = self.make_request('GET', f'/api/iot/v1/knowledge-bases/{self.kb_id}/documents?page=1&page_size=100')
        
        if not result or result.get("code") != 200:
            self.log("❌ 获取文档列表失败", "ERROR")
            return False
        
        documents = result.get("data", {}).get("documents", [])
        
        for doc in documents:
            doc_name = doc.get("name", "").lower()
            # 查找包含 test 和 5 和 pptx 的文档
            if ("test" in doc_name and "5" in doc_name and "pptx" in doc_name) or \
               ("test (5)" in doc_name) or ("test(5)" in doc_name):
                self.doc_id = doc.get("id")
                self.log(f"✅ 找到文档: {doc.get('name')} (ID: {self.doc_id})", "SUCCESS")
                return True
        
        self.log("❌ 未找到 test (5).pptx 文档", "ERROR")
        self.log("📋 可用的文档:")
        for doc in documents[:10]:  # 只显示前10个
            self.log(f"   - {doc.get('name')} (ID: {doc.get('id')})")
        
        return False

    def get_first_chunk(self) -> bool:
        """获取第一个分块"""
        self.log(f"🔍 获取文档 {self.doc_id} 的分块信息...")
        
        result = self.make_request('GET', f'/api/iot/v1/documents/{self.kb_id}/{self.doc_id}/chunks?page=1&page_size=10')
        
        if not result or result.get("code") != 200:
            self.log("❌ 获取分块列表失败", "ERROR")
            return False
        
        chunks = result.get("data", {}).get("chunks", [])
        
        if not chunks:
            self.log("❌ 文档没有分块", "ERROR")
            return False
        
        self.chunk_id = chunks[0].get("id")
        content_preview = chunks[0].get("content", "")[:100] + "..." if chunks[0].get("content") else "无内容"
        
        self.log(f"✅ 找到 {len(chunks)} 个分块", "SUCCESS")
        self.log(f"📄 将测试第一个分块: {self.chunk_id}")
        self.log(f"📝 当前内容预览: {content_preview}")
        
        return True

    def get_chunk_content(self) -> Optional[str]:
        """获取当前分块内容"""
        result = self.make_request('GET', f'/api/iot/v1/documents/{self.kb_id}/{self.doc_id}/chunks?page=1&page_size=50')
        
        if not result or result.get("code") != 200:
            return None
        
        chunks = result.get("data", {}).get("chunks", [])
        for chunk in chunks:
            if chunk.get("id") == self.chunk_id:
                return chunk.get("content", "")
        
        return None

    def test_chunk_update(self) -> bool:
        """测试分块更新功能"""
        self.log("🧪 开始测试分块更新功能...")
        
        # 1. 获取原始内容
        original_content = self.get_chunk_content()
        if original_content is None:
            self.log("❌ 无法获取原始分块内容", "ERROR")
            return False
        
        self.log(f"📖 原始内容长度: {len(original_content)} 字符")
        
        # 2. 生成测试内容
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        test_content = f"{original_content}\n\n[🧪 测试更新 - {timestamp}]"
        
        self.log(f"📝 生成测试内容，新长度: {len(test_content)} 字符")
        
        # 3. 执行更新
        update_data = {
            "content": test_content,
            "important_keywords": []
        }
        
        self.log(f"💾 执行分块更新...")
        result = self.make_request('PUT', f'/api/iot/v1/documents/{self.kb_id}/{self.doc_id}/chunks/{self.chunk_id}', update_data)
        
        if not result or result.get("code") != 200:
            self.log("❌ 分块更新失败", "ERROR")
            return False
        
        self.log("✅ 分块更新API调用成功", "SUCCESS")
        
        # 4. 验证更新（等待一段时间确保更新生效）
        self.log("⏳ 等待2秒后验证更新...")
        time.sleep(2)
        
        updated_content = self.get_chunk_content()
        if updated_content is None:
            self.log("❌ 无法获取更新后的分块内容", "ERROR")
            return False
        
        # 5. 检查更新是否生效
        if updated_content == test_content:
            self.log("🎉 验证成功：分块内容已正确更新！", "SUCCESS")
            success = True
        else:
            self.log("❌ 验证失败：分块内容未更新或不匹配", "ERROR")
            self.log(f"期望长度: {len(test_content)}, 实际长度: {len(updated_content)}")
            success = False
        
        # 6. 恢复原始内容
        self.log("🔄 恢复原始内容...")
        restore_data = {
            "content": original_content,
            "important_keywords": []
        }
        
        restore_result = self.make_request('PUT', f'/api/iot/v1/documents/{self.kb_id}/{self.doc_id}/chunks/{self.chunk_id}', restore_data)
        
        if restore_result and restore_result.get("code") == 200:
            time.sleep(1)
            final_content = self.get_chunk_content()
            if final_content == original_content:
                self.log("✅ 原始内容恢复成功", "SUCCESS")
            else:
                self.log("⚠️ 原始内容恢复验证失败", "WARNING")
        else:
            self.log("⚠️ 原始内容恢复失败", "WARNING")
        
        return success

    def run_test(self) -> bool:
        """运行完整测试"""
        self.log("🚀 开始测试 pythontest5 知识库中 test (5).pptx 的分块更新功能")
        self.log("="*80)
        
        # 1. 查找知识库
        if not self.find_pythontest5_kb():
            return False
        
        # 2. 查找文档
        if not self.find_test5_document():
            return False
        
        # 3. 获取分块
        if not self.get_first_chunk():
            return False
        
        # 4. 测试更新
        success = self.test_chunk_update()
        
        # 5. 输出结果
        self.log("="*80)
        if success:
            self.log("🎉 测试通过！分块更新功能正常工作。", "SUCCESS")
            self.log("✅ 确认修复点:")
            self.log("   - 后端正确处理RAGFlow API响应码")
            self.log("   - 前端正确刷新数据显示最新内容")
            self.log("   - 数据持久化正常工作")
        else:
            self.log("❌ 测试失败！分块更新功能仍有问题。", "ERROR")
            self.log("🔧 建议检查:")
            self.log("   - 后端日志中的RAGFlow API调用")
            self.log("   - 前端控制台中的调试信息")
            self.log("   - RAGFlow服务的连接状态")
        
        return success

def main():
    parser = argparse.ArgumentParser(description='测试 pythontest5 知识库中 test (5).pptx 的分块更新功能')
    parser.add_argument('--token', required=True, help='JWT认证token')
    
    args = parser.parse_args()
    
    tester = PythonTest5ChunkTester(token=args.token)
    success = tester.run_test()
    
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
