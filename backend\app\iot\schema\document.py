#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档管理相关的 Schema 定义

基于 RAGFlow API 规范设计的文档数据模型
严格遵循指南中的API优先策略和参数预处理策略
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field
from fastapi import UploadFile


class DocumentStatus(str, Enum):
    """文档状态枚举"""
    UPLOADING = "uploading"      # 上传中
    UPLOADED = "uploaded"        # 已上传
    PARSING = "parsing"          # 解析中
    PARSED = "parsed"           # 已解析
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"     # 已取消


class DocumentType(str, Enum):
    """文档类型枚举"""
    PDF = "pdf"
    DOCX = "docx"
    DOC = "doc"
    TXT = "txt"
    MD = "md"
    HTML = "html"
    XLSX = "xlsx"
    XLS = "xls"
    PPT = "ppt"
    PPTX = "pptx"
    CSV = "csv"
    JSON = "json"
    XML = "xml"


class DocumentInfo(BaseModel):
    """文档信息模型"""
    id: Optional[str] = Field(None, description="文档ID")
    name: str = Field(..., description="文档名称")
    type: Optional[DocumentType] = Field(None, description="文档类型")
    size: Optional[int] = Field(None, description="文件大小(字节)")
    status: Optional[DocumentStatus] = Field(None, description="文档状态")
    chunk_num: Optional[int] = Field(0, description="分块数量")
    token_num: Optional[int] = Field(0, description="Token数量")
    parser_id: Optional[str] = Field(None, description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")
    thumbnail: Optional[str] = Field(None, description="缩略图URL")
    progress: Optional[float] = Field(0.0, ge=0.0, le=1.0, description="处理进度(0-1)")
    progress_msg: Optional[str] = Field(None, description="进度消息")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")
    created_by: Optional[str] = Field(None, description="创建者")


class DocumentUpload(BaseModel):
    """文档上传请求"""
    kb_id: str = Field(..., description="知识库ID")
    parser_id: Optional[str] = Field("naive", description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")
    run_after_upload: bool = Field(True, description="上传后是否立即解析")


class DocumentUpdate(BaseModel):
    """文档更新请求"""
    name: Optional[str] = Field(None, description="文档名称")
    parser_id: Optional[str] = Field(None, description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")


class DocumentQuery(BaseModel):
    """文档查询参数"""
    kb_id: str = Field(..., description="知识库ID")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(30, ge=1, le=100, description="每页数量")
    orderby: str = Field("create_time", description="排序字段")
    desc: bool = Field(True, description="是否降序")
    keywords: Optional[str] = Field(None, description="关键词搜索")
    status: Optional[DocumentStatus] = Field(None, description="状态筛选")
    type: Optional[DocumentType] = Field(None, description="类型筛选")


class DocumentDelete(BaseModel):
    """文档删除请求"""
    kb_id: str = Field(..., description="知识库ID")
    doc_ids: List[str] = Field(..., description="文档ID列表")


class DocumentParseControl(BaseModel):
    """文档解析控制"""
    kb_id: str = Field(..., description="知识库ID")
    doc_id: str = Field(..., description="文档ID")
    parser_id: Optional[str] = Field(None, description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")


class DocumentParseStatus(BaseModel):
    """文档解析状态"""
    doc_id: str = Field(..., description="文档ID")
    status: DocumentStatus = Field(..., description="解析状态")
    progress: float = Field(0.0, ge=0.0, le=1.0, description="解析进度")
    progress_msg: Optional[str] = Field(None, description="进度消息")
    chunk_num: int = Field(0, description="已生成分块数量")
    token_num: int = Field(0, description="已生成Token数量")
    error_msg: Optional[str] = Field(None, description="错误信息")


class DocumentList(BaseModel):
    """文档列表响应"""
    documents: List[DocumentInfo] = Field(default_factory=list, description="文档列表")
    total: int = Field(0, description="总数量")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(30, description="每页数量")
    has_more: bool = Field(False, description="是否有更多数据")


class DocumentUploadResponse(BaseModel):
    """文档上传响应"""
    doc_id: str = Field(..., description="文档ID")
    name: str = Field(..., description="文档名称")
    size: int = Field(..., description="文件大小")
    status: DocumentStatus = Field(..., description="文档状态")
    message: str = Field("上传成功", description="响应消息")


class DocumentDownloadInfo(BaseModel):
    """文档下载信息"""
    doc_id: str = Field(..., description="文档ID")
    name: str = Field(..., description="文档名称")
    size: int = Field(..., description="文件大小")
    content_type: str = Field(..., description="内容类型")
    download_url: Optional[str] = Field(None, description="下载链接")


# RAGFlow API 参数预处理相关模型
class RAGFlowDocumentUpload(BaseModel):
    """RAGFlow文档上传参数（预处理后）"""
    parser_id: str = Field(default="naive", description="解析器ID")
    parser_config: Dict[str, Any] = Field(default_factory=dict, description="解析器配置")
    run: bool = Field(True, description="是否立即运行解析")


class RAGFlowDocumentUpdate(BaseModel):
    """RAGFlow文档更新参数（预处理后）"""
    name: Optional[str] = Field(None, description="文档名称")
    parser_id: Optional[str] = Field(None, description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")


class RAGFlowDocumentQuery(BaseModel):
    """RAGFlow文档查询参数（预处理后）"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(30, ge=1, le=100, description="每页数量")
    orderby: str = Field("create_time", description="排序字段")
    desc: bool = Field(True, description="是否降序")
    keywords: Optional[str] = Field(None, description="关键词搜索")


class RAGFlowDocumentDelete(BaseModel):
    """RAGFlow文档删除参数（预处理后）"""
    doc_ids: List[str] = Field(..., description="文档ID列表")


# 文件上传相关模型
class FileUploadProgress(BaseModel):
    """文件上传进度"""
    file_name: str = Field(..., description="文件名")
    total_size: int = Field(..., description="总大小")
    uploaded_size: int = Field(0, description="已上传大小")
    progress: float = Field(0.0, ge=0.0, le=1.0, description="上传进度")
    speed: Optional[str] = Field(None, description="上传速度")
    remaining_time: Optional[str] = Field(None, description="剩余时间")
    status: str = Field("uploading", description="上传状态")


class FileValidation(BaseModel):
    """文件验证配置"""
    max_size: int = Field(100 * 1024 * 1024, description="最大文件大小(字节)")  # 100MB
    allowed_types: List[str] = Field(
        default_factory=lambda: [
            "pdf", "docx", "doc", "txt", "md", "html",
            "xlsx", "xls", "ppt", "pptx", "csv", "json", "xml"
        ],
        description="允许的文件类型"
    )
    allowed_mime_types: List[str] = Field(
        default_factory=lambda: [
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
            "text/plain",
            "text/markdown",
            "text/html",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/csv",
            "application/json",
            "application/xml",
            "text/xml"
        ],
        description="允许的MIME类型"
    )

    def validate_file(self, file: UploadFile) -> bool:
        """验证上传的文件"""
        # 检查文件大小
        if hasattr(file, 'size') and file.size and file.size > self.max_size:
            return False

        # 检查文件扩展名
        if file.filename:
            ext = file.filename.split('.')[-1].lower()
            if ext not in self.allowed_types:
                return False

        # 检查MIME类型
        if file.content_type and file.content_type not in self.allowed_mime_types:
            return False

        return True


# 符合RAGFlow API规范的新模型

class RAGFlowDocumentListRequest(BaseModel):
    """RAGFlow文档列表请求参数"""
    keywords: Optional[str] = Field(None, description="用于匹配文档标题的关键词")
    page: int = Field(1, ge=1, description="指定将显示文档的页面")
    page_size: int = Field(30, ge=1, le=100, description="每页的文档数量")
    orderby: str = Field("create_time", description="文档应该按哪个字段排序")
    desc: bool = Field(True, description="表示检索到的文档是否应按降序排序")
    id: Optional[str] = Field(None, description="要检索的文档的ID")
    name: Optional[str] = Field(None, description="要检索的文档的名称")


class RAGFlowDocumentListResponse(BaseModel):
    """RAGFlow文档列表响应"""
    code: int = Field(..., description="响应代码")
    data: Dict[str, Any] = Field(..., description="响应数据")
    message: Optional[str] = Field(None, description="响应消息")


class RAGFlowDocumentUploadRequest(BaseModel):
    """RAGFlow文档上传请求参数"""
    # 文件通过multipart/form-data上传，这里不包含file字段
    pass


class RAGFlowDocumentUploadResponse(BaseModel):
    """RAGFlow文档上传响应"""
    code: int = Field(..., description="响应代码")
    data: List[Dict[str, Any]] = Field(..., description="上传结果列表")
    message: Optional[str] = Field(None, description="响应消息")


class RAGFlowDocumentDeleteRequest(BaseModel):
    """RAGFlow文档删除请求参数"""
    ids: Optional[List[str]] = Field(None, description="要删除的文档的ID。如果未指定，将删除指定数据集中的所有文档")


class RAGFlowDocumentDeleteResponse(BaseModel):
    """RAGFlow文档删除响应"""
    code: int = Field(..., description="响应代码")
    message: Optional[str] = Field(None, description="响应消息")


class RAGFlowDocumentUpdateRequest(BaseModel):
    """RAGFlow文档更新请求参数"""
    name: Optional[str] = Field(None, description="文档的名称")
    meta_fields: Optional[Dict[str, Any]] = Field(None, description="文档的元字段")
    chunk_method: Optional[str] = Field(None, description="应用于文档的分块方法")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="数据集解析器的配置设置")


class RAGFlowDocumentUpdateResponse(BaseModel):
    """RAGFlow文档更新响应"""
    code: int = Field(..., description="响应代码")
    message: Optional[str] = Field(None, description="响应消息")


class RAGFlowDocumentParseRequest(BaseModel):
    """RAGFlow文档解析请求参数"""
    document_ids: List[str] = Field(..., description="要解析的文档的ID")


class RAGFlowDocumentParseResponse(BaseModel):
    """RAGFlow文档解析响应"""
    code: int = Field(..., description="响应代码")
    message: Optional[str] = Field(None, description="响应消息")


class RAGFlowDocumentStopParseRequest(BaseModel):
    """RAGFlow停止文档解析请求参数"""
    document_ids: List[str] = Field(..., description="应停止解析的文档的ID")


class RAGFlowDocumentStopParseResponse(BaseModel):
    """RAGFlow停止文档解析响应"""
    code: int = Field(..., description="响应代码")
    message: Optional[str] = Field(None, description="响应消息")


# 通用响应模型
class RAGFlowBaseResponse(BaseModel):
    """RAGFlow基础响应模型"""
    code: int = Field(..., description="响应代码，0表示成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


# 文档详情模型（基于RAGFlow API规范）
class RAGFlowDocumentInfo(BaseModel):
    """RAGFlow文档信息模型"""
    id: Optional[str] = Field(None, description="文档ID")
    name: Optional[str] = Field(None, description="文档名称")
    type: Optional[str] = Field(None, description="文档类型")
    size: Optional[int] = Field(None, description="文件大小")
    chunk_num: Optional[int] = Field(None, description="分块数量")
    token_num: Optional[int] = Field(None, description="Token数量")
    parser_id: Optional[str] = Field(None, description="解析器ID")
    parser_config: Optional[Dict[str, Any]] = Field(None, description="解析器配置")
    status: Optional[str] = Field(None, description="文档状态")
    progress: Optional[float] = Field(None, description="处理进度")
    progress_msg: Optional[str] = Field(None, description="进度消息")
    create_time: Optional[int] = Field(None, description="创建时间戳")
    update_time: Optional[int] = Field(None, description="更新时间戳")
    created_by: Optional[str] = Field(None, description="创建者")


# ==================== 文档解析结果查看相关Schema ====================

class DocumentChunk(BaseModel):
    """文档分块信息模型"""
    id: str = Field(..., description="分块ID")
    content: str = Field(..., description="分块内容")
    content_ltks: Optional[str] = Field(None, description="分块内容（低级token）")
    document_id: str = Field(..., description="文档ID")
    document_keyword: Optional[str] = Field(None, description="文档关键词")
    highlight: Optional[str] = Field(None, description="高亮内容")
    image_id: Optional[str] = Field(None, description="图片ID")
    important_keywords: Optional[List[str]] = Field(None, description="重要关键词")
    kb_id: str = Field(..., description="知识库ID")
    positions: Optional[List[str]] = Field(None, description="位置信息")
    similarity: Optional[float] = Field(None, description="相似度")
    term_similarity: Optional[float] = Field(None, description="词项相似度")
    vector_similarity: Optional[float] = Field(None, description="向量相似度")
    token_count: Optional[int] = Field(None, description="Token数量")
    create_time: Optional[str] = Field(None, description="创建时间")
    update_time: Optional[str] = Field(None, description="更新时间")


class DocumentChunksResponse(BaseModel):
    """文档分块列表响应模型"""
    chunks: List[DocumentChunk] = Field(default_factory=list, description="分块列表")
    doc: Optional[RAGFlowDocumentInfo] = Field(None, description="文档信息")
    total: int = Field(0, description="总数量")


class DocumentChunksQuery(BaseModel):
    """文档分块查询参数模型"""
    dataset_id: str = Field(..., description="数据集ID")
    document_id: str = Field(..., description="文档ID")
    keywords: Optional[str] = Field(None, description="关键词搜索")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(50, ge=1, le=1024, description="每页数量")
    id: Optional[str] = Field(None, description="分块ID")


class DocumentRetrievalParams(BaseModel):
    """文档检索参数模型"""
    question: str = Field(..., description="检索问题")
    dataset_ids: Optional[List[str]] = Field(None, description="数据集ID列表")
    document_ids: Optional[List[str]] = Field(None, description="文档ID列表")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(30, ge=1, le=100, description="每页数量")
    similarity_threshold: float = Field(0.2, ge=0.0, le=1.0, description="相似度阈值")
    vector_similarity_weight: float = Field(0.3, ge=0.0, le=1.0, description="向量相似度权重")
    top_k: int = Field(1024, ge=1, description="参与向量计算的分块数")
    rerank_id: Optional[str] = Field(None, description="重排序模型ID")
    keyword: bool = Field(False, description="是否启用关键词匹配")
    highlight: bool = Field(False, description="是否高亮显示")


class DocumentRetrievalResponse(BaseModel):
    """文档检索响应模型"""
    chunks: List[DocumentChunk] = Field(default_factory=list, description="检索到的分块")
    doc_aggs: List[Dict[str, Any]] = Field(default_factory=list, description="文档聚合信息")
    total: int = Field(0, description="总数量")


class DocumentParseResult(BaseModel):
    """文档解析结果模型"""
    document: Optional[RAGFlowDocumentInfo] = Field(None, description="文档信息")
    chunk_count: int = Field(0, description="分块总数")
    chunks_preview: List[DocumentChunk] = Field(default_factory=list, description="分块预览")


# ==================== 文档分块CRUD操作相关Schema ====================

class DocumentChunkCreateRequest(BaseModel):
    """文档分块创建请求模型"""
    content: str = Field(..., description="分块的文本内容")
    important_keywords: Optional[List[str]] = Field(None, description="要与分块一起标记的关键术语或短语")
    questions: Optional[List[str]] = Field(None, description="如果有给定问题，嵌入的分块将基于它们")


class DocumentChunkUpdateRequest(BaseModel):
    """文档分块更新请求模型"""
    content: Optional[str] = Field(None, description="分块的文本内容")
    important_keywords: Optional[List[str]] = Field(None, description="要与分块一起标记的关键术语或短语列表")
    available: Optional[bool] = Field(True, description="分块在数据集中的可用性状态")


class DocumentChunkDeleteRequest(BaseModel):
    """文档分块删除请求模型"""
    chunk_ids: List[str] = Field(..., description="要删除的分块ID列表")


class DocumentChunkBatchOperation(BaseModel):
    """文档分块批量操作模型"""
    operation: str = Field(..., description="操作类型：create, update, delete")
    chunks: List[Dict[str, Any]] = Field(..., description="分块数据列表")


class DocumentChunkEditState(BaseModel):
    """文档分块编辑状态模型"""
    chunk_id: str = Field(..., description="分块ID")
    is_editing: bool = Field(False, description="是否正在编辑")
    original_content: Optional[str] = Field(None, description="原始内容")
    modified_content: Optional[str] = Field(None, description="修改后的内容")
    is_modified: bool = Field(False, description="是否已修改")
    last_modified: Optional[str] = Field(None, description="最后修改时间")
