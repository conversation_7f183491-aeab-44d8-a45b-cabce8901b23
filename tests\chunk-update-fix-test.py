#!/usr/bin/env python3
"""
分块更新功能修复验证测试脚本

使用方法:
python chunk-update-fix-test.py --base-url http://localhost:8000 --token your_jwt_token --dataset-id your_dataset_id --document-id your_document_id --chunk-id your_chunk_id
"""

import argparse
import requests
import json
import time
from typing import Dict, Any

class ChunkUpdateFixTester:
    def __init__(self, base_url: str, token: str, dataset_id: str, document_id: str, chunk_id: str):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.dataset_id = dataset_id
        self.document_id = document_id
        self.chunk_id = chunk_id
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def log(self, message: str, level: str = "INFO"):
        """打印日志"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")

    def make_request(self, method: str, endpoint: str, data: Dict[Any, Any] = None) -> Dict[Any, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=self.headers, json=data)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            self.log(f"{method} {endpoint} - Status: {response.status_code}")
            
            if response.status_code >= 400:
                self.log(f"请求失败: {response.text}", "ERROR")
                return {"error": response.text, "status_code": response.status_code}

            return response.json()

        except Exception as e:
            self.log(f"请求异常: {str(e)}", "ERROR")
            return {"error": str(e)}

    def get_chunk_content(self) -> str:
        """获取当前分块内容"""
        self.log("📖 获取当前分块内容...")
        
        endpoint = f"/api/iot/v1/documents/{self.dataset_id}/{self.document_id}/chunks"
        params = "?page=1&page_size=100"
        
        result = self.make_request('GET', endpoint + params)
        
        if "error" in result:
            self.log("❌ 获取分块列表失败", "ERROR")
            return ""
        
        if result.get("code") == 200:
            chunks = result.get("data", {}).get("chunks", [])
            for chunk in chunks:
                if chunk.get("id") == self.chunk_id:
                    content = chunk.get("content", "")
                    self.log(f"✅ 找到目标分块，当前内容长度: {len(content)} 字符")
                    self.log(f"📄 内容预览: {content[:100]}...")
                    return content
            
            self.log(f"❌ 未找到分块ID: {self.chunk_id}", "ERROR")
            return ""
        else:
            self.log(f"❌ 获取分块列表失败: {result.get('msg', '未知错误')}", "ERROR")
            return ""

    def update_chunk_content(self, new_content: str) -> bool:
        """更新分块内容"""
        self.log(f"✏️ 更新分块内容: {self.chunk_id}")
        
        endpoint = f"/api/iot/v1/documents/{self.dataset_id}/{self.document_id}/chunks/{self.chunk_id}"
        
        update_data = {
            "content": new_content,
            "important_keywords": []
        }
        
        self.log(f"📤 发送更新请求: {update_data}")
        
        result = self.make_request('PUT', endpoint, update_data)
        
        if "error" in result:
            self.log("❌ 更新分块失败", "ERROR")
            return False
        
        if result.get("code") == 200:
            self.log(f"✅ 分块更新API调用成功")
            return True
        else:
            self.log(f"❌ 分块更新失败: {result.get('msg', '未知错误')}", "ERROR")
            return False

    def verify_update(self, expected_content: str) -> bool:
        """验证更新是否生效"""
        self.log("🔍 验证更新是否生效...")
        
        # 等待一段时间确保更新生效
        time.sleep(2)
        
        actual_content = self.get_chunk_content()
        
        if actual_content == expected_content:
            self.log("✅ 验证成功：分块内容已正确更新", "SUCCESS")
            return True
        else:
            self.log("❌ 验证失败：分块内容未更新或不匹配", "ERROR")
            self.log(f"期望内容: {expected_content[:100]}...")
            self.log(f"实际内容: {actual_content[:100]}...")
            return False

    def run_update_test(self):
        """运行完整的更新测试"""
        self.log("🚀 开始分块更新功能修复验证测试")
        self.log(f"📋 测试参数:")
        self.log(f"   - API地址: {self.base_url}")
        self.log(f"   - 数据集ID: {self.dataset_id}")
        self.log(f"   - 文档ID: {self.document_id}")
        self.log(f"   - 分块ID: {self.chunk_id}")
        
        # 1. 获取原始内容
        original_content = self.get_chunk_content()
        if not original_content:
            self.log("❌ 无法获取原始分块内容，测试终止", "ERROR")
            return False
        
        # 2. 生成测试内容
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        test_content = f"{original_content}\n\n[测试更新 - {timestamp}]"
        
        self.log(f"📝 生成测试内容，长度: {len(test_content)} 字符")
        
        # 3. 执行更新
        update_success = self.update_chunk_content(test_content)
        if not update_success:
            self.log("❌ 更新操作失败，测试终止", "ERROR")
            return False
        
        # 4. 验证更新
        verify_success = self.verify_update(test_content)
        
        # 5. 恢复原始内容
        self.log("🔄 恢复原始内容...")
        restore_success = self.update_chunk_content(original_content)
        if restore_success:
            restore_verify = self.verify_update(original_content)
            if restore_verify:
                self.log("✅ 原始内容恢复成功")
            else:
                self.log("⚠️ 原始内容恢复验证失败", "WARNING")
        else:
            self.log("⚠️ 原始内容恢复失败", "WARNING")
        
        # 6. 输出测试结果
        if verify_success:
            self.log("🎉 测试通过！分块更新功能修复成功。", "SUCCESS")
            self.log("✅ 确认修复点:")
            self.log("   - 后端正确处理RAGFlow API响应码")
            self.log("   - 前端正确刷新数据显示最新内容")
            self.log("   - 数据持久化正常工作")
        else:
            self.log("❌ 测试失败！分块更新功能仍有问题。", "ERROR")
            self.log("🔧 建议检查:")
            self.log("   - 后端日志中的RAGFlow API调用")
            self.log("   - 前端控制台中的调试信息")
            self.log("   - RAGFlow服务的连接状态")
        
        return verify_success

def main():
    parser = argparse.ArgumentParser(description='分块更新功能修复验证测试')
    parser.add_argument('--base-url', required=True, help='API基础URL')
    parser.add_argument('--token', required=True, help='JWT认证token')
    parser.add_argument('--dataset-id', required=True, help='数据集ID')
    parser.add_argument('--document-id', required=True, help='文档ID')
    parser.add_argument('--chunk-id', required=True, help='要测试的分块ID')
    
    args = parser.parse_args()
    
    tester = ChunkUpdateFixTester(
        base_url=args.base_url,
        token=args.token,
        dataset_id=args.dataset_id,
        document_id=args.document_id,
        chunk_id=args.chunk_id
    )
    
    success = tester.run_update_test()
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
