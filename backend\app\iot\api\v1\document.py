#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档管理 API

提供文档的CRUD操作接口，基于Java token认证系统进行权限控制
严格遵循RAGFlow API规范和指南中的开发原则
"""
import uuid
import io
import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, UploadFile, File, Form
from fastapi.responses import StreamingResponse, Response

from backend.common.log import log as logger
from backend.app.iot.schema.document import (
    DocumentUpload,
    DocumentUpdate,
    DocumentQuery,
    DocumentDelete,
    DocumentParseControl,
    DocumentInfo,
    DocumentList,
    DocumentUploadResponse,
    FileUploadProgress,
    DocumentChunkCreateRequest,
    DocumentChunkUpdateRequest,
    DocumentChunkDeleteRequest
)
from backend.app.iot.service.document_service import document_service
from backend.app.iot.utils.file_upload import file_upload_handler
from backend.common.response.response_schema import ResponseModel, response_base
from backend.common.response.response_code import CustomResponse
from backend.common.security.jwt import DependsJwtAuth
from backend.common.security.java_permission import require_java_permission

router = APIRouter()


# 特定路由必须在通配符路由之前定义，避免路由冲突

@router.get(
    '/upload-progress/{upload_id}',
    summary='查询上传进度',
    response_model=ResponseModel,
    name='get_upload_progress'
)
async def get_upload_progress(
    upload_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    查询文件上传进度

    不需要特殊权限，只要有有效token即可
    """
    try:
        # 这里可以从缓存或数据库中获取进度信息
        # 目前返回一个示例响应
        progress_data = {
            "upload_id": upload_id,
            "status": "completed",
            "progress": 1.0,
            "message": "上传完成"
        }

        return response_base.success(
            res=CustomResponse(200, "获取上传进度成功"),
            data=progress_data
        )

    except Exception as e:
        logger.error(f"获取上传进度失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取上传进度失败: {str(e)}")
        )


@router.get(
    '/conversion-progress/{task_id}',
    summary='查询文档转换进度',
    response_model=ResponseModel,
    name='get_conversion_progress'
)
async def get_conversion_progress(
    task_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    查询文档转换进度

    不需要特殊权限，只要有有效token即可
    """
    try:
        result = await document_service.get_conversion_progress(task_id)

        return response_base.success(
            res=CustomResponse(200, "获取转换进度成功"),
            data=result
        )

    except Exception as e:
        logger.error(f"获取转换进度失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取转换进度失败: {str(e)}")
        )


@router.get(
    '/cache/stats',
    summary='获取缓存统计信息',
    response_model=ResponseModel,
    name='get_cache_stats'
)
@require_java_permission("knowledge:base:view")
async def get_cache_stats(
    request: Request,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    获取文档预览缓存统计信息

    需要knowledge:base:view权限
    """
    try:
        result = await document_service.get_cache_stats()

        return response_base.success(
            res=CustomResponse(200, "获取缓存统计成功"),
            data=result
        )

    except Exception as e:
        logger.error(f"获取缓存统计失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取缓存统计失败: {str(e)}")
        )


# 通配符路由，必须放在最后

@router.delete(
    '/{kb_id}/{doc_id}/cache',
    summary='清除文档缓存',
    response_model=ResponseModel,
    name='clear_document_cache'
)
@require_java_permission("knowledge:base:update")
async def clear_document_cache(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    清除指定文档的缓存

    需要knowledge:base:update权限
    """
    try:
        result = await document_service.clear_document_cache(doc_id)

        return response_base.success(
            res=CustomResponse(200, "清除缓存成功"),
            data=result
        )

    except Exception as e:
        logger.error(f"清除文档缓存失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"清除文档缓存失败: {str(e)}")
        )


# 符合RAGFlow API规范的文档管理接口

@router.post(
    '/{dataset_id}/documents',
    summary='上传文档到数据集',
    response_model=ResponseModel,
    name='upload_documents_ragflow'
)
@require_java_permission("knowledge:base:create")
async def upload_documents(
    request: Request,
    dataset_id: str,
    file: List[UploadFile] = File(..., description="上传的文件列表"),
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    上传文档到指定数据集 - 符合RAGFlow API规范

    需要knowledge:base:create权限

    支持的文件类型：PDF、Word、Excel、TXT、Markdown等
    支持批量上传
    """
    try:
        # 调用文档服务上传多个文件
        result = await document_service.upload_documents(dataset_id, file)

        return response_base.success(
            res=CustomResponse(200, "文档上传成功"),
            data=result.get("data", [])
        )

    except HTTPException as e:
        logger.error(f"上传文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"上传文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"上传文档失败: {str(e)}")
        )


@router.get(
    '/{dataset_id}/documents',
    summary='列出数据集中的文档',
    response_model=ResponseModel,
    name='list_documents_ragflow'
)
@require_java_permission("knowledge:base:list")
async def list_documents(
    request: Request,
    dataset_id: str,
    keywords: Optional[str] = None,
    page: int = 1,
    page_size: int = 30,
    orderby: str = "create_time",
    desc: bool = True,
    id: Optional[str] = None,
    name: Optional[str] = None,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    列出指定数据集中的文档 - 符合RAGFlow API规范

    需要knowledge:base:list权限

    支持分页、搜索、排序等功能
    """
    try:
        # 构建查询参数
        query_params = {
            "dataset_id": dataset_id,
            "keywords": keywords,
            "page": page,
            "page_size": page_size,
            "orderby": orderby,
            "desc": desc,
            "id": id,
            "name": name
        }

        result = await document_service.list_documents_ragflow(query_params)

        return response_base.success(
            res=CustomResponse(200, "获取文档列表成功"),
            data=result.get("data", {})
        )

    except HTTPException as e:
        logger.error(f"获取文档列表失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档列表失败: {str(e)}")
        )


@router.get(
    '/{kb_id}/list',
    summary='获取文档列表（兼容性端点）',
    response_model=ResponseModel,
    name='list_documents_legacy'
)
@require_java_permission("knowledge:base:list")
async def list_documents_legacy(
    request: Request,
    kb_id: str,
    query: DocumentQuery = Depends(),
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    获取指定知识库的文档列表（兼容性端点）

    需要knowledge:base:list权限

    支持分页、搜索、排序等功能
    """
    try:
        # 设置知识库ID
        query.kb_id = kb_id

        result = await document_service.list_documents(query)

        return response_base.success(
            res=CustomResponse(200, "获取文档列表成功"),
            data=result.get("data", [])
        )

    except HTTPException as e:
        logger.error(f"获取文档列表失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档列表失败: {str(e)}")
        )


@router.post(
    '/{kb_id}/upload',
    summary='上传文档到知识库（兼容性端点）',
    response_model=ResponseModel,
    name='upload_document_legacy'
)
@require_java_permission("knowledge:base:create")
async def upload_document_legacy(
    request: Request,
    kb_id: str,
    file: UploadFile = File(..., description="上传的文件"),
    parser_id: str = Form("naive", description="解析器ID"),
    run_after_upload: bool = Form(True, description="上传后是否立即解析"),
    upload_id: Optional[str] = Form(None, description="上传ID，用于进度跟踪"),
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    上传文档到指定知识库（兼容性端点）

    需要knowledge:base:create权限

    支持的文件类型：PDF、Word、Excel、TXT、Markdown等
    支持上传进度跟踪
    """
    try:
        # 生成上传ID（如果未提供）
        if not upload_id:
            upload_id = str(uuid.uuid4())

        # 构建上传参数
        upload_data = DocumentUpload(
            kb_id=kb_id,
            parser_id=parser_id,
            run_after_upload=run_after_upload
        )

        result = await document_service.upload_document(kb_id, file, upload_data, upload_id)

        # 处理RAGFlow API响应（data字段可能是列表）
        response_data = result.get("data", [])

        # 构建统一的响应格式
        if isinstance(response_data, list):
            # 如果是列表，包装成标准格式
            formatted_data = {
                "upload_id": upload_id,
                "documents": response_data,
                "count": len(response_data),
                "success": True
            }
        elif isinstance(response_data, dict):
            # 如果是字典，添加upload_id
            formatted_data = response_data.copy()
            formatted_data["upload_id"] = upload_id
        else:
            # 其他情况，创建标准响应
            formatted_data = {
                "upload_id": upload_id,
                "result": response_data,
                "success": True
            }

        return response_base.success(
            res=CustomResponse(200, "文档上传成功"),
            data=formatted_data
        )

    except HTTPException as e:
        logger.error(f"上传文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"上传文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"上传文档失败: {str(e)}")
        )


@router.delete(
    '/{dataset_id}/documents',
    summary='删除数据集中的文档',
    response_model=ResponseModel,
    name='delete_documents_ragflow'
)
@require_java_permission("knowledge:base:delete")
async def delete_documents_ragflow(
    request: Request,
    dataset_id: str,
    delete_data: dict,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    删除指定数据集中的文档 - 符合RAGFlow API规范

    需要knowledge:base:delete权限

    支持批量删除
    """
    try:
        result = await document_service.delete_documents_ragflow(dataset_id, delete_data.get("ids", []))

        return response_base.success(
            res=CustomResponse(200, "删除文档成功"),
            data=result.get("data")
        )

    except HTTPException as e:
        logger.error(f"删除文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"删除文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"删除文档失败: {str(e)}")
        )


@router.put(
    '/{dataset_id}/documents/{document_id}',
    summary='更新文档信息',
    response_model=ResponseModel,
    name='update_document_ragflow'
)
@require_java_permission("knowledge:base:update")
async def update_document_ragflow(
    request: Request,
    dataset_id: str,
    document_id: str,
    update_data: dict,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    更新指定文档的信息 - 符合RAGFlow API规范

    需要knowledge:base:update权限
    """
    try:
        result = await document_service.update_document_ragflow(dataset_id, document_id, update_data)

        return response_base.success(
            res=CustomResponse(200, "更新文档成功"),
            data=result.get("data")
        )

    except HTTPException as e:
        logger.error(f"更新文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"更新文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"更新文档失败: {str(e)}")
        )


@router.get(
    '/{dataset_id}/documents/{document_id}',
    summary='下载文档',
    name='download_document_ragflow'
)
@require_java_permission("knowledge:base:view")
async def download_document_ragflow(
    request: Request,
    dataset_id: str,
    document_id: str,
    token: str = DependsJwtAuth
):
    """
    下载指定文档 - 符合RAGFlow API规范

    需要knowledge:base:view权限
    """
    try:
        result = await document_service.download_document_ragflow(dataset_id, document_id)

        # 返回文件流
        return StreamingResponse(
            result.get("content"),
            media_type=result.get("content_type", "application/octet-stream"),
            headers={
                "Content-Disposition": f"attachment; filename={result.get('filename', 'document')}"
            }
        )

    except HTTPException as e:
        logger.error(f"下载文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"下载文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"下载文档失败: {str(e)}")
        )


@router.post(
    '/{dataset_id}/chunks',
    summary='解析文档',
    response_model=ResponseModel,
    name='parse_documents_ragflow'
)
@require_java_permission("knowledge:base:update")
async def parse_documents(
    request: Request,
    dataset_id: str,
    parse_data: dict,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    解析指定数据集中的文档 - 符合RAGFlow API规范

    需要knowledge:base:update权限
    """
    try:
        result = await document_service.parse_documents_ragflow(dataset_id, parse_data.get("document_ids", []))

        return response_base.success(
            res=CustomResponse(200, "开始解析文档"),
            data=result.get("data")
        )

    except HTTPException as e:
        logger.error(f"解析文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"解析文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"解析文档失败: {str(e)}")
        )


@router.delete(
    '/{dataset_id}/chunks',
    summary='停止解析文档',
    response_model=ResponseModel,
    name='stop_parsing_documents_ragflow'
)
@require_java_permission("knowledge:base:update")
async def stop_parsing_documents(
    request: Request,
    dataset_id: str,
    stop_data: dict,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    停止解析指定的文档 - 符合RAGFlow API规范

    需要knowledge:base:update权限
    """
    try:
        result = await document_service.stop_parsing_documents_ragflow(dataset_id, stop_data.get("document_ids", []))

        return response_base.success(
            res=CustomResponse(200, "停止解析文档"),
            data=result.get("data")
        )

    except HTTPException as e:
        logger.error(f"停止解析文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"停止解析文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"停止解析文档失败: {str(e)}")
        )


@router.get(
    '/{kb_id}/{doc_id}',
    summary='获取文档详情',
    response_model=ResponseModel,
    name='get_document'
)
@require_java_permission("knowledge:base:view")
async def get_document(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    获取指定文档的详细信息
    
    需要knowledge:document:view权限
    """
    try:
        result = await document_service.get_document(kb_id, doc_id)
        
        return response_base.success(
            res=CustomResponse(200, "获取文档详情成功"),
            data=result.get("data")
        )
        
    except HTTPException as e:
        logger.error(f"获取文档详情失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档详情失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档详情失败: {str(e)}")
        )


@router.put(
    '/{kb_id}/{doc_id}',
    summary='更新文档信息',
    response_model=ResponseModel,
    name='update_document'
)
@require_java_permission("knowledge:base:update")
async def update_document(
    request: Request,
    kb_id: str,
    doc_id: str,
    update_data: DocumentUpdate,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    更新指定文档的信息
    
    需要knowledge:document:update权限
    """
    try:
        result = await document_service.update_document(kb_id, doc_id, update_data)
        
        return response_base.success(
            res=CustomResponse(200, "更新文档成功"),
            data=result.get("data")
        )
        
    except HTTPException as e:
        logger.error(f"更新文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"更新文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"更新文档失败: {str(e)}")
        )


@router.delete(
    '/{kb_id}/delete',
    summary='删除文档',
    response_model=ResponseModel,
    name='delete_documents'
)
@require_java_permission("knowledge:base:delete")
async def delete_documents(
    request: Request,
    kb_id: str,
    delete_request: dict,  # 改为接收简单的字典
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    删除指定的文档

    需要knowledge:base:delete权限

    支持批量删除

    请求体格式: {"doc_ids": ["doc_id1", "doc_id2"]}
    """
    try:
        # 从请求体中获取文档ID列表
        doc_ids = delete_request.get("doc_ids", [])
        if not doc_ids:
            return response_base.fail(
                res=CustomResponse(400, "doc_ids参数不能为空")
            )

        # 构建删除数据对象
        delete_data = DocumentDelete(
            kb_id=kb_id,
            doc_ids=doc_ids
        )

        result = await document_service.delete_documents(delete_data)

        # 调试：打印删除服务的响应
        logger.info(f"删除服务响应: {result}")
        logger.info(f"删除服务响应类型: {type(result)}")

        # 构建删除成功的响应
        response_data = {
            "deleted_count": len(doc_ids),
            "deleted_ids": doc_ids,
            "success": True
        }

        # 如果RAGFlow返回了额外数据，也包含进来
        if result and "data" in result:
            response_data["ragflow_result"] = result["data"]

        logger.info(f"准备返回的响应数据: {response_data}")

        success_response = response_base.success(
            res=CustomResponse(200, "删除文档成功"),
            data=response_data
        )

        logger.info(f"最终响应: {success_response}")

        return success_response

    except HTTPException as e:
        logger.error(f"删除文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"删除文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"删除文档失败: {str(e)}")
        )


@router.post(
    '/{kb_id}/{doc_id}/parse',
    summary='开始文档解析',
    response_model=ResponseModel,
    name='start_document_parsing'
)
@require_java_permission("knowledge:base:update")
async def start_document_parsing(
    request: Request,
    kb_id: str,
    doc_id: str,
    parse_request: dict,  # 改为接收简单的字典
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    开始解析指定文档

    需要knowledge:base:update权限

    请求体格式: {"parser_id": "naive", "parser_config": {...}}
    """
    try:
        # 构建解析控制对象
        parse_control = DocumentParseControl(
            kb_id=kb_id,
            doc_id=doc_id,
            parser_id=parse_request.get("parser_id", "naive"),
            parser_config=parse_request.get("parser_config")
        )

        result = await document_service.start_document_parsing(parse_control)

        return response_base.success(
            res=CustomResponse(200, "开始文档解析"),
            data=result.get("data")
        )

    except HTTPException as e:
        logger.error(f"开始文档解析失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"开始文档解析失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"开始文档解析失败: {str(e)}")
        )


@router.delete(
    '/{kb_id}/{doc_id}/parse',
    summary='停止文档解析',
    response_model=ResponseModel,
    name='stop_document_parsing'
)
@require_java_permission("knowledge:base:update")
async def stop_document_parsing(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    停止解析指定文档

    需要knowledge:document:parse权限
    """
    try:
        result = await document_service.stop_document_parsing(kb_id, doc_id)

        return response_base.success(
            res=CustomResponse(200, "停止文档解析"),
            data=result.get("data")
        )

    except HTTPException as e:
        logger.error(f"停止文档解析失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"停止文档解析失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"停止文档解析失败: {str(e)}")
        )


# ==================== 文档解析结果查看相关API ====================

@router.get(
    '/{dataset_id}/{document_id}/chunks',
    summary='获取文档分块列表',
    response_model=ResponseModel,
    name='get_document_chunks'
)
@require_java_permission("knowledge:base:view")
async def get_document_chunks(
    request: Request,
    dataset_id: str,
    document_id: str,
    keywords: Optional[str] = None,
    page: int = 1,
    page_size: int = 50,
    id: Optional[str] = None,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    获取指定文档的分块列表 - 符合RAGFlow API规范

    需要knowledge:base:view权限

    支持分页、搜索等功能
    """
    try:
        # 构建查询参数
        query_params = {
            "keywords": keywords,
            "page": page,
            "page_size": page_size,
            "id": id
        }

        result = await document_service.get_document_chunks(dataset_id, document_id, query_params)

        return response_base.success(
            res=CustomResponse(200, "获取文档分块列表成功"),
            data=result.get("data", {})
        )

    except HTTPException as e:
        logger.error(f"获取文档分块列表失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档分块列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档分块列表失败: {str(e)}")
        )


@router.post(
    '/retrieval',
    summary='检索文档分块',
    response_model=ResponseModel,
    name='retrieve_document_chunks'
)
@require_java_permission("knowledge:base:view")
async def retrieve_document_chunks(
    request: Request,
    retrieval_data: dict,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    检索文档分块（带搜索功能） - 符合RAGFlow API规范

    需要knowledge:base:view权限

    支持向量检索、关键词匹配、相似度过滤等功能
    """
    try:
        result = await document_service.retrieve_document_chunks(retrieval_data)

        return response_base.success(
            res=CustomResponse(200, "检索文档分块成功"),
            data=result.get("data", {})
        )

    except HTTPException as e:
        logger.error(f"检索文档分块失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"检索文档分块失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"检索文档分块失败: {str(e)}")
        )


@router.get(
    '/{dataset_id}/{document_id}/parse-result',
    summary='获取文档解析结果统计',
    response_model=ResponseModel,
    name='get_document_parse_result'
)
@require_java_permission("knowledge:base:view")
async def get_document_parse_result(
    request: Request,
    dataset_id: str,
    document_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    获取文档解析结果统计信息

    需要knowledge:base:view权限

    返回文档的解析统计信息，如分块数量、Token数量等
    """
    try:
        result = await document_service.get_document_parse_result(dataset_id, document_id)

        return response_base.success(
            res=CustomResponse(200, "获取文档解析结果成功"),
            data=result.get("data", {})
        )

    except HTTPException as e:
        logger.error(f"获取文档解析结果失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档解析结果失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档解析结果失败: {str(e)}")
        )


# ==================== 文档分块CRUD操作API ====================

@router.post(
    '/{dataset_id}/{document_id}/chunks/create',
    summary='创建文档分块',
    response_model=ResponseModel,
    name='create_document_chunk'
)
@require_java_permission("knowledge:base:update")
async def create_document_chunk(
    request: Request,
    dataset_id: str,
    document_id: str,
    chunk_data: DocumentChunkCreateRequest,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    创建新的文档分块 - 符合RAGFlow API规范

    需要knowledge:base:update权限
    """
    try:
        result = await document_service.create_document_chunk(dataset_id, document_id, chunk_data)

        return response_base.success(
            res=CustomResponse(200, "创建文档分块成功"),
            data=result.get("data", {})
        )

    except HTTPException as e:
        logger.error(f"创建文档分块失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"创建文档分块失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"创建文档分块失败: {str(e)}")
        )


@router.put(
    '/{dataset_id}/{document_id}/chunks/{chunk_id}',
    summary='更新文档分块',
    response_model=ResponseModel,
    name='update_document_chunk'
)
@require_java_permission("knowledge:base:update")
async def update_document_chunk(
    request: Request,
    dataset_id: str,
    document_id: str,
    chunk_id: str,
    chunk_data: DocumentChunkUpdateRequest,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    更新指定文档分块 - 符合RAGFlow API规范

    需要knowledge:base:update权限
    """
    try:
        result = await document_service.update_document_chunk(dataset_id, document_id, chunk_id, chunk_data)

        return response_base.success(
            res=CustomResponse(200, "更新文档分块成功"),
            data=result.get("data", {})
        )

    except HTTPException as e:
        logger.error(f"更新文档分块失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"更新文档分块失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"更新文档分块失败: {str(e)}")
        )


@router.delete(
    '/{dataset_id}/{document_id}/chunks/delete',
    summary='删除文档分块',
    response_model=ResponseModel,
    name='delete_document_chunks'
)
@require_java_permission("knowledge:base:update")
async def delete_document_chunks(
    request: Request,
    dataset_id: str,
    document_id: str,
    delete_data: DocumentChunkDeleteRequest,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    删除指定文档分块 - 符合RAGFlow API规范

    需要knowledge:base:update权限
    """
    try:
        result = await document_service.delete_document_chunks(dataset_id, document_id, delete_data)

        return response_base.success(
            res=CustomResponse(200, "删除文档分块成功"),
            data=result.get("data", {})
        )

    except HTTPException as e:
        logger.error(f"删除文档分块失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"删除文档分块失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"删除文档分块失败: {str(e)}")
        )





@router.get(
    '/{kb_id}/{doc_id}/download',
    summary='下载文档',
    name='download_document'
)
@require_java_permission("knowledge:base:view")
async def download_document(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
):
    """
    下载指定文档

    需要knowledge:base:view权限
    """
    try:
        result = await document_service.download_document(kb_id, doc_id)

        # 返回文件流
        return StreamingResponse(
            result.get("content"),
            media_type=result.get("content_type", "application/octet-stream"),
            headers={
                "Content-Disposition": f"attachment; filename={result.get('filename', 'document')}"
            }
        )

    except HTTPException as e:
        logger.error(f"下载文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"下载文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"下载文档失败: {str(e)}")
        )


@router.get("/{kb_id}/{doc_id}/content", summary="获取文档内容用于预览")
@require_java_permission("knowledge:base:view")
async def get_document_content(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
):
    """
    获取文档内容用于预览（不带下载头信息）

    专门为vue-office等预览组件提供文档内容
    需要knowledge:base:view权限
    """
    try:
        result = await document_service.download_document(kb_id, doc_id)

        # 获取完整的文件内容
        content_stream = result.get("content")
        if hasattr(content_stream, 'getvalue'):
            # 如果是BytesIO，获取完整内容
            file_content = content_stream.getvalue()
        elif hasattr(content_stream, 'read'):
            # 如果是其他流对象，读取完整内容
            file_content = content_stream.read()
        else:
            # 如果已经是bytes
            file_content = content_stream

        # 直接返回完整文件内容，不使用流式传输
        return Response(
            content=file_content,
            media_type=result.get("content_type", "application/octet-stream"),
            headers={
                "Cache-Control": "public, max-age=3600",  # 允许缓存1小时
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET",
                "Access-Control-Allow-Headers": "Authorization, Content-Type"
            }
        )

    except HTTPException as e:
        logger.error(f"获取文档内容失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档内容失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档内容失败: {str(e)}")
        )






@router.get(
    '/{kb_id}/{doc_id}/preview',
    summary='获取文档预览',
    name='preview_document'
)
@require_java_permission("knowledge:base:view")
async def preview_document(
    request: Request,
    kb_id: str,
    doc_id: str,
    doc_name: str = None,
    token: str = DependsJwtAuth
):
    """
    获取文档预览内容

    需要knowledge:base:view权限
    """
    try:
        result = await document_service.get_document_preview(kb_id, doc_id, doc_name)

        # 统一返回JSON格式响应
        return response_base.success(
            res=CustomResponse(200, "获取预览成功"),
            data=result
        )

    except HTTPException as e:
        logger.error(f"获取文档预览失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档预览失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档预览失败: {str(e)}")
        )



