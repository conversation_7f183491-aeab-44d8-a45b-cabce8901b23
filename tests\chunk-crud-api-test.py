#!/usr/bin/env python3
"""
文档分块CRUD功能API测试脚本

使用方法:
python chunk-crud-api-test.py --base-url http://localhost:8000 --token your_jwt_token --dataset-id your_dataset_id --document-id your_document_id
"""

import argparse
import requests
import json
import time
from typing import Dict, Any, List

class ChunkCRUDTester:
    def __init__(self, base_url: str, token: str, dataset_id: str, document_id: str):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.dataset_id = dataset_id
        self.document_id = document_id
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        self.created_chunk_ids = []

    def log(self, message: str, level: str = "INFO"):
        """打印日志"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")

    def make_request(self, method: str, endpoint: str, data: Dict[Any, Any] = None) -> Dict[Any, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=self.headers, json=data)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=self.headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=self.headers, json=data)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            self.log(f"{method} {endpoint} - Status: {response.status_code}")
            
            if response.status_code >= 400:
                self.log(f"请求失败: {response.text}", "ERROR")
                return {"error": response.text, "status_code": response.status_code}

            return response.json()

        except Exception as e:
            self.log(f"请求异常: {str(e)}", "ERROR")
            return {"error": str(e)}

    def test_get_chunks(self) -> bool:
        """测试获取分块列表"""
        self.log("🔍 测试获取分块列表...")
        
        endpoint = f"/api/iot/v1/documents/{self.dataset_id}/{self.document_id}/chunks"
        params = "?page=1&page_size=10"
        
        result = self.make_request('GET', endpoint + params)
        
        if "error" in result:
            self.log("❌ 获取分块列表失败", "ERROR")
            return False
        
        if result.get("code") == 200:
            chunks = result.get("data", {}).get("chunks", [])
            self.log(f"✅ 成功获取 {len(chunks)} 个分块")
            return True
        else:
            self.log(f"❌ 获取分块列表失败: {result.get('msg', '未知错误')}", "ERROR")
            return False

    def test_create_chunk(self) -> str:
        """测试创建分块"""
        self.log("➕ 测试创建分块...")
        
        endpoint = f"/api/iot/v1/documents/{self.dataset_id}/{self.document_id}/chunks/create"
        
        test_data = {
            "content": "这是一个测试分块的内容，用于验证分块创建功能。",
            "important_keywords": ["测试", "分块", "创建"],
            "questions": ["这是什么内容？", "如何测试分块功能？"]
        }
        
        result = self.make_request('POST', endpoint, test_data)
        
        if "error" in result:
            self.log("❌ 创建分块失败", "ERROR")
            return ""
        
        if result.get("code") == 200:
            chunk_data = result.get("data", {})
            chunk_id = chunk_data.get("chunk", {}).get("id", "")
            if chunk_id:
                self.created_chunk_ids.append(chunk_id)
                self.log(f"✅ 成功创建分块: {chunk_id}")
                return chunk_id
            else:
                self.log("❌ 创建分块成功但未返回分块ID", "WARNING")
                return ""
        else:
            self.log(f"❌ 创建分块失败: {result.get('msg', '未知错误')}", "ERROR")
            return ""

    def test_update_chunk(self, chunk_id: str) -> bool:
        """测试更新分块"""
        if not chunk_id:
            self.log("⚠️ 跳过更新测试：没有有效的分块ID", "WARNING")
            return False
            
        self.log(f"✏️ 测试更新分块: {chunk_id}")
        
        endpoint = f"/api/iot/v1/documents/{self.dataset_id}/{self.document_id}/chunks/{chunk_id}"
        
        update_data = {
            "content": "这是更新后的分块内容，验证分块更新功能正常工作。",
            "important_keywords": ["更新", "测试", "分块", "修改"],
            "available": True
        }
        
        result = self.make_request('PUT', endpoint, update_data)
        
        if "error" in result:
            self.log("❌ 更新分块失败", "ERROR")
            return False
        
        if result.get("code") == 200:
            self.log(f"✅ 成功更新分块: {chunk_id}")
            return True
        else:
            self.log(f"❌ 更新分块失败: {result.get('msg', '未知错误')}", "ERROR")
            return False

    def test_delete_chunk(self, chunk_id: str) -> bool:
        """测试删除分块"""
        if not chunk_id:
            self.log("⚠️ 跳过删除测试：没有有效的分块ID", "WARNING")
            return False
            
        self.log(f"🗑️ 测试删除分块: {chunk_id}")
        
        endpoint = f"/api/iot/v1/documents/{self.dataset_id}/{self.document_id}/chunks/delete"
        
        delete_data = {
            "chunk_ids": [chunk_id]
        }
        
        result = self.make_request('DELETE', endpoint, delete_data)
        
        if "error" in result:
            self.log("❌ 删除分块失败", "ERROR")
            return False
        
        if result.get("code") == 200:
            self.log(f"✅ 成功删除分块: {chunk_id}")
            if chunk_id in self.created_chunk_ids:
                self.created_chunk_ids.remove(chunk_id)
            return True
        else:
            self.log(f"❌ 删除分块失败: {result.get('msg', '未知错误')}", "ERROR")
            return False

    def test_batch_delete_chunks(self, chunk_ids: List[str]) -> bool:
        """测试批量删除分块"""
        if not chunk_ids:
            self.log("⚠️ 跳过批量删除测试：没有有效的分块ID", "WARNING")
            return False
            
        self.log(f"🗑️ 测试批量删除分块: {chunk_ids}")
        
        endpoint = f"/api/iot/v1/documents/{self.dataset_id}/{self.document_id}/chunks/delete"
        
        delete_data = {
            "chunk_ids": chunk_ids
        }
        
        result = self.make_request('DELETE', endpoint, delete_data)
        
        if "error" in result:
            self.log("❌ 批量删除分块失败", "ERROR")
            return False
        
        if result.get("code") == 200:
            self.log(f"✅ 成功批量删除 {len(chunk_ids)} 个分块")
            for chunk_id in chunk_ids:
                if chunk_id in self.created_chunk_ids:
                    self.created_chunk_ids.remove(chunk_id)
            return True
        else:
            self.log(f"❌ 批量删除分块失败: {result.get('msg', '未知错误')}", "ERROR")
            return False

    def run_full_test(self):
        """运行完整的CRUD测试"""
        self.log("🚀 开始文档分块CRUD功能测试")
        self.log(f"📋 测试参数:")
        self.log(f"   - API地址: {self.base_url}")
        self.log(f"   - 数据集ID: {self.dataset_id}")
        self.log(f"   - 文档ID: {self.document_id}")
        
        test_results = {
            "get_chunks": False,
            "create_chunk": False,
            "update_chunk": False,
            "delete_chunk": False,
            "batch_delete": False
        }
        
        # 1. 测试获取分块列表
        test_results["get_chunks"] = self.test_get_chunks()
        
        # 2. 测试创建分块
        chunk_id = self.test_create_chunk()
        test_results["create_chunk"] = bool(chunk_id)
        
        # 3. 测试更新分块
        if chunk_id:
            test_results["update_chunk"] = self.test_update_chunk(chunk_id)
        
        # 4. 创建更多分块用于批量测试
        additional_chunks = []
        for i in range(2):
            additional_chunk = self.test_create_chunk()
            if additional_chunk:
                additional_chunks.append(additional_chunk)
        
        # 5. 测试单个删除
        if chunk_id:
            test_results["delete_chunk"] = self.test_delete_chunk(chunk_id)
        
        # 6. 测试批量删除
        if additional_chunks:
            test_results["batch_delete"] = self.test_batch_delete_chunks(additional_chunks)
        
        # 清理剩余的测试分块
        if self.created_chunk_ids:
            self.log(f"🧹 清理剩余的测试分块: {self.created_chunk_ids}")
            self.test_batch_delete_chunks(self.created_chunk_ids)
        
        # 输出测试结果
        self.log("📊 测试结果汇总:")
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            self.log(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        self.log(f"🎯 测试完成: {passed}/{total} 通过")
        
        if passed == total:
            self.log("🎉 所有测试通过！分块CRUD功能正常工作。")
        else:
            self.log("⚠️ 部分测试失败，请检查API实现和配置。")
        
        return passed == total

def main():
    parser = argparse.ArgumentParser(description='文档分块CRUD功能API测试')
    parser.add_argument('--base-url', required=True, help='API基础URL')
    parser.add_argument('--token', required=True, help='JWT认证token')
    parser.add_argument('--dataset-id', required=True, help='数据集ID')
    parser.add_argument('--document-id', required=True, help='文档ID')
    
    args = parser.parse_args()
    
    tester = ChunkCRUDTester(
        base_url=args.base_url,
        token=args.token,
        dataset_id=args.dataset_id,
        document_id=args.document_id
    )
    
    success = tester.run_full_test()
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
