#!/usr/bin/env python3
"""
获取JWT Token的辅助脚本

使用方法:
python get-jwt-token.py --username admin --password your_password

或者从浏览器获取:
1. 打开浏览器开发者工具 (F12)
2. 登录系统
3. 在Network标签页中找到任意API请求
4. 查看Request Headers中的Authorization字段
5. 复制 "Bearer " 后面的token部分
"""

import argparse
import requests
import json

def get_jwt_token(username: str, password: str, base_url: str = "http://localhost:8000") -> str:
    """通过用户名密码获取JWT token"""
    
    login_url = f"{base_url}/api/v1/auth/login"
    
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        print(f"🔐 尝试登录: {username}")
        response = requests.post(login_url, json=login_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                token = result.get("data", {}).get("access_token")
                if token:
                    print(f"✅ 登录成功！")
                    print(f"🎫 JWT Token: {token}")
                    print(f"\n📋 测试命令:")
                    print(f"python test-pythontest5-chunk-update.py --token {token}")
                    return token
                else:
                    print("❌ 登录响应中没有找到token")
            else:
                print(f"❌ 登录失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ 登录请求失败: {response.status_code} - {response.text}")
    
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
    
    return ""

def main():
    parser = argparse.ArgumentParser(description='获取JWT Token')
    parser.add_argument('--username', default='admin', help='用户名 (默认: admin)')
    parser.add_argument('--password', required=True, help='密码')
    parser.add_argument('--base-url', default='http://localhost:8000', help='API基础URL')
    
    args = parser.parse_args()
    
    print("🚀 获取JWT Token...")
    print("="*50)
    
    token = get_jwt_token(args.username, args.password, args.base_url)
    
    if not token:
        print("\n💡 如果自动获取失败，可以手动从浏览器获取:")
        print("1. 打开浏览器开发者工具 (F12)")
        print("2. 登录系统")
        print("3. 在Network标签页中找到任意API请求")
        print("4. 查看Request Headers中的Authorization字段")
        print("5. 复制 'Bearer ' 后面的token部分")
        print("6. 运行: python test-pythontest5-chunk-update.py --token YOUR_TOKEN")

if __name__ == "__main__":
    main()
