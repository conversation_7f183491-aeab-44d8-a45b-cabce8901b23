#!/usr/bin/env python3
"""
获取测试参数的辅助脚本
用于查找 pythontest5 知识库中的 test (5).pptx 文档和其分块信息
"""

import requests
import json
import sys
import os

# 配置信息
BASE_URL = "http://localhost:8000"
# 这里需要替换为实际的JWT token
JWT_TOKEN = "your_jwt_token_here"

def get_headers():
    return {
        'Authorization': f'Bearer {JWT_TOKEN}',
        'Content-Type': 'application/json'
    }

def make_request(method, endpoint, data=None):
    """发送HTTP请求"""
    url = f"{BASE_URL}{endpoint}"
    headers = get_headers()
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers)
        elif method.upper() == 'POST':
            response = requests.post(url, headers=headers, json=data)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")

        print(f"{method} {endpoint} - Status: {response.status_code}")
        
        if response.status_code >= 400:
            print(f"请求失败: {response.text}")
            return None

        return response.json()

    except Exception as e:
        print(f"请求异常: {str(e)}")
        return None

def find_knowledge_base():
    """查找 pythontest5 知识库"""
    print("🔍 查找 pythontest5 知识库...")
    
    result = make_request('GET', '/api/iot/v1/knowledge-bases?page=1&page_size=100')
    
    if not result or result.get("code") != 200:
        print("❌ 获取知识库列表失败")
        return None
    
    knowledge_bases = result.get("data", {}).get("knowledge_bases", [])
    
    for kb in knowledge_bases:
        if "pythontest5" in kb.get("name", "").lower():
            print(f"✅ 找到知识库: {kb.get('name')} (ID: {kb.get('id')})")
            return kb.get("id")
    
    print("❌ 未找到 pythontest5 知识库")
    print("📋 可用的知识库:")
    for kb in knowledge_bases:
        print(f"   - {kb.get('name')} (ID: {kb.get('id')})")
    
    return None

def find_document(kb_id):
    """查找 test (5).pptx 文档"""
    print(f"🔍 在知识库 {kb_id} 中查找 test (5).pptx 文档...")
    
    result = make_request('GET', f'/api/iot/v1/knowledge-bases/{kb_id}/documents?page=1&page_size=100')
    
    if not result or result.get("code") != 200:
        print("❌ 获取文档列表失败")
        return None
    
    documents = result.get("data", {}).get("documents", [])
    
    for doc in documents:
        doc_name = doc.get("name", "")
        if "test" in doc_name.lower() and "5" in doc_name and "pptx" in doc_name.lower():
            print(f"✅ 找到文档: {doc_name} (ID: {doc.get('id')})")
            return doc.get("id")
    
    print("❌ 未找到 test (5).pptx 文档")
    print("📋 可用的文档:")
    for doc in documents:
        print(f"   - {doc.get('name')} (ID: {doc.get('id')})")
    
    return None

def get_document_chunks(kb_id, doc_id):
    """获取文档分块信息"""
    print(f"🔍 获取文档 {doc_id} 的分块信息...")
    
    result = make_request('GET', f'/api/iot/v1/documents/{kb_id}/{doc_id}/chunks?page=1&page_size=10')
    
    if not result or result.get("code") != 200:
        print("❌ 获取分块列表失败")
        return []
    
    chunks = result.get("data", {}).get("chunks", [])
    
    print(f"✅ 找到 {len(chunks)} 个分块")
    for i, chunk in enumerate(chunks):
        chunk_id = chunk.get("id", "")
        content_preview = chunk.get("content", "")[:50] + "..." if chunk.get("content") else "无内容"
        print(f"   分块 {i+1}: {chunk_id} - {content_preview}")
    
    return chunks

def main():
    print("🚀 获取测试参数...")
    
    # 检查JWT token
    if JWT_TOKEN == "your_jwt_token_here":
        print("❌ 请先设置正确的JWT_TOKEN")
        print("💡 提示: 可以从浏览器开发者工具的Network标签页中获取Authorization header")
        return
    
    # 1. 查找知识库
    kb_id = find_knowledge_base()
    if not kb_id:
        return
    
    # 2. 查找文档
    doc_id = find_document(kb_id)
    if not doc_id:
        return
    
    # 3. 获取分块信息
    chunks = get_document_chunks(kb_id, doc_id)
    if not chunks:
        return
    
    # 4. 输出测试命令
    first_chunk_id = chunks[0].get("id") if chunks else ""
    
    print("\n" + "="*60)
    print("🎯 测试参数获取成功！")
    print("="*60)
    print(f"知识库ID: {kb_id}")
    print(f"文档ID: {doc_id}")
    print(f"第一个分块ID: {first_chunk_id}")
    
    print("\n📋 运行测试命令:")
    print(f"python tests/chunk-update-fix-test.py \\")
    print(f"  --base-url {BASE_URL} \\")
    print(f"  --token {JWT_TOKEN} \\")
    print(f"  --dataset-id {kb_id} \\")
    print(f"  --document-id {doc_id} \\")
    print(f"  --chunk-id {first_chunk_id}")
    
    print("\n💡 提示: 复制上面的命令并在终端中运行")

if __name__ == "__main__":
    main()
